import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class  settingService {
  apiUrl = `${environment.apiUrl}`;

  constructor(private http: HttpClient) { }

   getAllRolls(params?: {
    page?: number;
    size?: number;
    search?: string;
    sortBy?: string;
    sortDir?: string;
  }): Observable<any> {
    let httpParams = new HttpParams();

    if (params) {
      if (params.page !== undefined) httpParams = httpParams.set('offset', (params.page * (params.size || 10)).toString());
      if (params.size !== undefined) httpParams = httpParams.set('limit', params.size.toString());
      if (params.search) httpParams = httpParams.set('name', params.search);
      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);
      if (params.sortDir) httpParams = httpParams.set('sort', params.sortDir);
    } else {
      // Default parameters
      httpParams = httpParams
        .set('limit', '10')
        .set('offset', '0')
        .set('sort', 'ASC')
        .set('sortBy', 'id');
    }

    return this.http.get<any>(`${environment.apiUrl}/authorization/list-roles`, { params: httpParams });
  }

  createRoles(roleData: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/authorization/create-role`, roleData);
  }


  getAllPermissions(params?: {
    page?: number;
    size?: number;
    search?: string;
    sortBy?: string;
    sortDir?: string;
  }): Observable<any> {
    let httpParams = new HttpParams();

    if (params) {
      if (params.page !== undefined) httpParams = httpParams.set('offset', (params.page * (params.size || 10)).toString());
      if (params.size !== undefined) httpParams = httpParams.set('limit', params.size.toString());
      if (params.search) httpParams = httpParams.set('name', params.search);
      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);
      if (params.sortDir) httpParams = httpParams.set('sort', params.sortDir);
    } else {
      // Default parameters
      httpParams = httpParams
        .set('limit', '10')
        .set('offset', '0')
        .set('sort', 'ASC')
        .set('sortBy', 'id');
    }

    return this.http.get<any>(`${environment.apiUrl}/authorization/list-permissions`, { params: httpParams });
  }

  AllPermission(): Observable<any> {
    const params = new HttpParams()
      .set('limit', '100')
      .set('offset', '0')
      .set('sort', 'ASC')
      .set('sortBy', 'id');

    return this.http.get<any>(`${environment.apiUrl}/authorization/list-permissions`, { params });
  }

  updateRolePermissions(roleId: number, permissions: string[]): Observable<any> {
    const body = {
      permissions: permissions
    };

    return this.http.post<any>(`${environment.apiUrl}/authorization/revoke-permission-to-role/${roleId}`, body);
  }

}
