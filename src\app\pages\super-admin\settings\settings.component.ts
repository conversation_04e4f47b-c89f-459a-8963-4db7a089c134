import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {

  adminFeatures = [
    {
      faIcon: 'fa-solid fa-users',
      title: 'User Account Settings',
      description: 'Manage user account types and permissions',
      backgroundColor: 'bg-light-primary',
      iconColor: 'text-primary',
      routePath: '/super-admin/user-account-types'
    },
    {
      faIcon: 'fa-solid fa-cube',
      title: 'Subscription Settings',
      description: 'Configure subscription types for brokers',
      backgroundColor: 'bg-light-success',
      iconColor: 'text-success',
      routePath: '/super-admin/subscriptions'
    },
    {
      faIcon: 'fa-solid fa-user-large',
      title: 'User Settings',
      description: 'Configure user profiles and electronic verification',
      backgroundColor: 'bg-light-info',
      iconColor: 'text-info',
      routePath: '/profile'
    }
  ];

  constructor(private router: Router) { }

  onCardClick(routePath: string): void {
    this.router.navigate([routePath]);
  }

  ngOnInit(): void {
  }

}
