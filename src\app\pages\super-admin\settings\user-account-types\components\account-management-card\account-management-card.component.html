<div class="card-body p-10 d-flex flex-column flex-center text-center" (click)="onCardClick()">
  <div class="mb-7">
    <div class="symbol symbol-circle symbol-100px">
      <div class="symbol-label bg-light-dark-blue">
        <i *ngIf="isFontAwesome" [class]="faIcon + ' fs-3x text-dark-blue'"></i>
      </div>
    </div>
  </div>

  <div class="text-center">
    <h3 class="fs-2 fw-bold text-gray-900 mb-4">{{ title }}</h3>
    <p class="fs-6 fw-semibold text-gray-600 mb-6">{{ description }}</p>
    <div class="bg-light-dark-blue rounded p-4 mb-6">
      <div class="fs-2x fw-bold text-dark-blue">{{ count }}</div>
      <div class="fs-7 fw-semibold text-gray-600 text-uppercase">{{ countLabel }}</div>
    </div>
  </div>
</div>