<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="d-flex align-items-center">
              <i class="fas fa-shield-alt text-success me-3 fs-2"></i>
              <div>
                <h3 class="mb-0">Permissions Control</h3>
                <span class="text-muted fs-6">Manage system permissions and access rights</span>
              </div>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm me-2" (click)="goBack()">
              <i class="fas fa-arrow-left me-2"></i>
              Back
            </button>
            <button class="btn btn-success btn-sm" (click)="addPermission()">
              <i class="fas fa-plus me-2"></i>
              Add Permission
            </button>
          </div>
        </div>


      </div>
    </div>
  </div>

  <!-- Permissions Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">All Permissions</h3>
          <div class="card-toolbar">
            <div class="d-flex align-items-center">
              <div class="position-relative me-3">
                <i class="fas fa-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                <input type="text" class="form-control form-control-sm ps-10" placeholder="Search permissions..."
                  [(ngModel)]="searchText" (input)="onSearchChange(searchText)">
              </div>

            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-row-dashed table-row-gray-300 gy-7">
              <thead>
                <tr class="fw-bold fs-6 text-gray-800">
                  <th>Permission Details</th>
                  <th>Created Date</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngIf="permissions.length === 0">
                  <td colspan="2" class="text-center py-5">
                    <div class="text-muted">
                      <i class="fas fa-shield-alt fs-2x mb-3"></i>
                      <div>{{ searchText ? 'No permissions match your search' : 'No permissions found' }}</div>
                    </div>
                  </td>
                </tr>
                <tr *ngFor="let permission of permissions">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="symbol symbol-45px me-3">
                        <div class="symbol-label bg-light-primary">
                          <i class="fas fa-shield-alt text-primary fs-3"></i>
                        </div>
                      </div>
                      <div>
                        <div class="fw-bold text-gray-800 fs-6">{{ permission.displayName }}</div>
                        <div class="text-muted fs-7 fw-semibold">{{ permission.name }}</div>
                        <div class="text-muted fs-8 mt-1" *ngIf="permission.description">{{ permission.description }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="text-muted fs-7">{{ permission.createdAt | date:'short' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination -->
        <div *ngIf="!loading && permissions.length > 0" class="d-flex justify-content-center mt-5 mb-5">
          <app-pagination [totalItems]="totalElements" [itemsPerPage]="pageSize" [currentPage]="currentPage"
            (pageChange)="onPageChange($event)"></app-pagination>
        </div>
      </div>
    </div>
  </div>
</div>