<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="d-flex align-items-center">
              <i class="fas fa-shield-alt text-success me-3 fs-2"></i>
              <div>
                <h3 class="mb-0">Permissions Control</h3>
                <span class="text-muted fs-6">Manage system permissions and access rights</span>
              </div>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm me-2" (click)="goBack()">
              <i class="fas fa-arrow-left me-2"></i>
              Back
            </button>
            <button class="btn btn-success btn-sm" (click)="addPermission()">
              <i class="fas fa-plus me-2"></i>
              Add Permission
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Permissions Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">All Permissions</h3>
          <div class="card-toolbar">
            <div class="d-flex align-items-center">
              <div class="position-relative me-3">
                <i class="fas fa-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                <input type="text" class="form-control form-control-sm ps-10" placeholder="Search permissions...">
              </div>

            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-row-dashed table-row-gray-300 gy-7">
              <thead>
                <tr class="fw-bold fs-6 text-gray-800">
                  <th>Permission</th>

                  <th>Created Date</th>

                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let permission of permissions">
                  <td>
                    <div>
                      <div class="fw-bold text-gray-800">{{ permission.displayName }}</div>
                      <div class="text-muted fs-7">{{ permission.name }}</div>
                      <div class="text-muted fs-8 mt-1">{{ permission.description }}</div>
                    </div>
                  </td>

                  <td class="text-muted">{{ permission.createdAt }}</td>

                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>