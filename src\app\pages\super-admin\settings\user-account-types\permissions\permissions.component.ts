import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-permissions',
  templateUrl: './permissions.component.html',
  styleUrls: ['./permissions.component.scss']
})
export class PermissionsComponent implements OnInit {

  // Sample permissions data
  permissions = [
    {
      id: 1,
      name: 'users.create',
      displayName: 'Create Users',
      description: 'Allow creating new user accounts',
      module: 'User Management',
      status: 'Active',
      createdAt: '2024-01-10'
    },
    {
      id: 2,
      name: 'users.edit',
      displayName: 'Edit Users',
      description: 'Allow editing existing user accounts',
      module: 'User Management',
      status: 'Active',
      createdAt: '2024-01-10'
    },
    {
      id: 3,
      name: 'users.delete',
      displayName: 'Delete Users',
      description: 'Allow deleting user accounts',
      module: 'User Management',
      status: 'Active',
      createdAt: '2024-01-10'
    },
    {
      id: 4,
      name: 'properties.manage',
      displayName: 'Manage Properties',
      description: 'Full access to property management',
      module: 'Property Management',
      status: 'Active',
      createdAt: '2024-01-12'
    },
    {
      id: 5,
      name: 'subscriptions.view',
      displayName: 'View Subscriptions',
      description: 'Allow viewing subscription plans',
      module: 'Subscription Management',
      status: 'Inactive',
      createdAt: '2024-01-08'
    },
    {
      id: 6,
      name: 'reports.generate',
      displayName: 'Generate Reports',
      description: 'Allow generating system reports',
      module: 'Reporting',
      status: 'Active',
      createdAt: '2024-01-15'
    }
  ];

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  addPermission(): void {
    // Navigate to add permission form or open modal
    console.log('Add new permission');
  }






  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

}
