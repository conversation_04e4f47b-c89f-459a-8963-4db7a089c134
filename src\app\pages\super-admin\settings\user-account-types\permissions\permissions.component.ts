import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-permissions',
  templateUrl: './permissions.component.html',
  styleUrls: ['./permissions.component.scss']
})
export class PermissionsComponent implements OnInit {

  permissions: any[] = [];

  // Search and pagination properties (following all-developers pattern)
  loading = false;
  searchText = '';
  currentPage = 0;
  pageSize = 10;
  totalElements = 0;
  totalPages = 0;

  // Sort options
  sortBy = 'id';
  sortDir = 'desc';

  constructor(private router: Router, private cd: ChangeDetectorRef, private settingService: settingService) { }

  ngOnInit(): void {
    this.loadPermissions();
  }

  loadPermissions(): void {
    this.loading = true;

    const params = {
      page: this.currentPage,
      size: this.pageSize,
      search: this.searchText || undefined,
      sortBy: this.sortBy,
      sortDir: this.sortDir
    };

    this.settingService.getAllPermissions(params).subscribe({
      next: (response) => {
        console.log(response);
        this.permissions = response.data || [];
        this.totalElements = response.count || response.totalElements || 0;
        this.totalPages = Math.ceil(this.totalElements / this.pageSize);
        this.loading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading permissions:', error);
        this.loading = false;
        Swal.fire('Error', 'Failed to load permissions. Please try again.', 'error');
      }
    });
  }

  onSearchChange(searchText: string): void {
    this.searchText = searchText;
    this.currentPage = 0;
    this.loadPermissions();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadPermissions();
  }

  addPermission(): void {
    Swal.fire({
      title: 'Add New Permission',
      html: `
        <input id="name" class="swal2-input" placeholder="Permission Name">
        <textarea id="description" class="swal2-textarea" placeholder="Description"></textarea>
      `,
      showCancelButton: true,
      confirmButtonText: 'Create',
      preConfirm: () => {
        const name = (document.getElementById('name') as HTMLInputElement).value;
        const description = (document.getElementById('description') as HTMLTextAreaElement).value;

        if (!name) {
          Swal.showValidationMessage('Permission name is required!');
          return false;
        }

        return { name, description };
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        this.settingService.createPermission(result.value).subscribe({
          next: () => {
            Swal.fire('Success!', 'Permission created successfully!', 'success').then(() => {
              this.loadPermissions();
            });
          },
          error: () => {
            Swal.fire('Error!', 'Failed to create permission.', 'error');
          }
        });
      }
    });
  }






  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

}
