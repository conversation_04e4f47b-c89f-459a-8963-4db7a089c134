import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-permissions',
  templateUrl: './permissions.component.html',
  styleUrls: ['./permissions.component.scss']
})
export class PermissionsComponent implements OnInit {

  // Sample permissions data
  permissions = [
    {
      id: 1,
      name: 'users.create',
      displayName: 'Create Users',
      description: 'Allow creating new user accounts',
      module: 'User Management',
      status: 'Active',
      createdAt: '2024-01-10'
    },
    {
      id: 2,
      name: 'users.edit',
      displayName: 'Edit Users',
      description: 'Allow editing existing user accounts',
      module: 'User Management',
      status: 'Active',
      createdAt: '2024-01-10'
    },
    {
      id: 3,
      name: 'users.delete',
      displayName: 'Delete Users',
      description: 'Allow deleting user accounts',
      module: 'User Management',
      status: 'Active',
      createdAt: '2024-01-10'
    },
    {
      id: 4,
      name: 'properties.manage',
      displayName: 'Manage Properties',
      description: 'Full access to property management',
      module: 'Property Management',
      status: 'Active',
      createdAt: '2024-01-12'
    },
    {
      id: 5,
      name: 'subscriptions.view',
      displayName: 'View Subscriptions',
      description: 'Allow viewing subscription plans',
      module: 'Subscription Management',
      status: 'Inactive',
      createdAt: '2024-01-08'
    },
    {
      id: 6,
      name: 'reports.generate',
      displayName: 'Generate Reports',
      description: 'Allow generating system reports',
      module: 'Reporting',
      status: 'Active',
      createdAt: '2024-01-15'
    }
  ];

  // Search and pagination properties
  filteredPermissions: any[] = [];
  searchTerm: string = '';

  // Pagination properties
  loading: boolean = false;
  totalElements: number = 0;
  pageSize: number = 10;
  currentPage: number = 0;

  constructor(private router: Router, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.initializeData();
  }

  initializeData(): void {
    this.filteredPermissions = [...this.permissions];
    this.totalElements = this.permissions.length;
    this.cd.detectChanges();
  }

  onSearchChange(): void {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.filteredPermissions = [...this.permissions];
    } else {
      const searchTermLower = this.searchTerm.toLowerCase().trim();
      this.filteredPermissions = this.permissions.filter(permission =>
        (permission.displayName && permission.displayName.toLowerCase().includes(searchTermLower)) ||
        (permission.name && permission.name.toLowerCase().includes(searchTermLower)) ||
        (permission.description && permission.description.toLowerCase().includes(searchTermLower)) ||
        (permission.module && permission.module.toLowerCase().includes(searchTermLower))
      );
    }
    this.cd.detectChanges();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    // In a real application, you would reload data from API here
    this.initializeData();
  }

  addPermission(): void {
    Swal.fire({
      title: 'Add New Permission',
      html: `
        <input id="permissionName" class="swal2-input" placeholder="Permission Name (e.g., users.create)">
        <input id="displayName" class="swal2-input" placeholder="Display Name (e.g., Create Users)">
        <textarea id="description" class="swal2-textarea" placeholder="Description"></textarea>
        <input id="module" class="swal2-input" placeholder="Module (e.g., User Management)">
      `,
      showCancelButton: true,
      confirmButtonText: 'Create Permission',
      cancelButtonText: 'Cancel',
      preConfirm: () => {
        const permissionName = (document.getElementById('permissionName') as HTMLInputElement).value;
        const displayName = (document.getElementById('displayName') as HTMLInputElement).value;
        const description = (document.getElementById('description') as HTMLTextAreaElement).value;
        const module = (document.getElementById('module') as HTMLInputElement).value;

        if (!permissionName || !displayName) {
          Swal.showValidationMessage('Permission name and display name are required!');
          return false;
        }

        return { permissionName, displayName, description, module };
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const newPermission = {
          id: this.permissions.length + 1,
          name: result.value.permissionName,
          displayName: result.value.displayName,
          description: result.value.description,
          module: result.value.module,
          status: 'Active',
          createdAt: new Date().toISOString().split('T')[0]
        };

        this.permissions.push(newPermission);
        this.initializeData();

        Swal.fire({
          title: 'Success!',
          text: 'Permission created successfully!',
          icon: 'success',
          timer: 2000,
          showConfirmButton: false
        });
      }
    });
  }






  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

}
