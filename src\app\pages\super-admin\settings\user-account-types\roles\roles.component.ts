import { ChangeDetectorRef, Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  roles: any[] = [];
  filteredRoles: any[] = [];
  searchTerm: string = '';
  openDropdownId: number | null = null;
  selectedRole: any = null;
  originalPermissions: any[] = [];
  showPermissionsModal: boolean = false;
  allPermissions: any[] = [];

  constructor(private router: Router, private settingService: settingService, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.lodingAllRoles();
    this.loadAllPermissions();
  }

lodingAllRoles() {
    this.settingService.getAllRolls().subscribe({
      next: (response) => {
        this.roles = response.data || [];
        this.filteredRoles = [...this.roles];
        this.cd.detectChanges();
      },
      error: (error) => {
        Swal.fire('Error loading roles:', error);
        this.roles = [];
        this.filteredRoles = [];
        this.cd.detectChanges();
      }
    });
  }

  addRole(): void {
    Swal.fire({
      title: 'Add New Role',
      input: 'text',
      inputLabel: 'Role Name',
      inputPlaceholder: 'Enter role name...',
      showCancelButton: true,
      confirmButtonText: 'Create Role',
      cancelButtonText: 'Cancel',
      inputValidator: (value) => {
        if (!value || value.trim() === '') {
          return 'Role name is required!';
        }
        return null;
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const roleData = {
          role: result.value.trim()
        };

        this.settingService.createRoles(roleData).subscribe({
          next: (response) => {
            Swal.fire({
              title: 'Success!',
              text: 'Role created successfully!',
              icon: 'success',
              timer: 2000,
              showConfirmButton: false
            }).then(() => {
              this.lodingAllRoles();
            });
          },
          error: (error) => {
            console.error('Error creating role:', error);
            Swal.fire({
              title: 'Error!',
              text: 'Failed to create role. Please try again.',
              icon: 'error'
            });
          }
        });
      }
    });
  }

  onSearchChange(): void {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.filteredRoles = [...this.roles];
    } else {
      const searchTermLower = this.searchTerm.toLowerCase().trim();
      this.filteredRoles = this.roles.filter(role =>
        role.name && role.name.toLowerCase().includes(searchTermLower)
      );
    }
    this.cd.detectChanges();
  }

  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);


    const role = this.roles.find(r => r.id === id);
    if (role) {

      this.selectedRole = JSON.parse(JSON.stringify(role));


      if (!this.selectedRole.permissions) {
        this.selectedRole.permissions = [];
      }


      this.allPermissions = this.allPermissions.map(permission => {

        const roleHasPermission = this.selectedRole.permissions.some((rolePermission: any) => {

          const permissionId = typeof rolePermission === 'string' ? rolePermission : rolePermission.id || rolePermission.name;
          return permissionId === permission.id || permissionId === permission.name;
        });

        return {
          ...permission,
          isSelected: roleHasPermission
        };
      });


      this.originalPermissions = JSON.parse(JSON.stringify(this.allPermissions));


      this.showPermissionsModal = true;
    }
  }




  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

  toggleDropdown(roleId: number): void {
    this.openDropdownId = this.openDropdownId === roleId ? null : roleId;
  }

  isDropdownOpen(roleId: number): boolean {
    return this.openDropdownId === roleId;
  }

  // Permissions Modal Methods
  areAllPermissionsSelected(): boolean {
    if (!this.allPermissions || this.allPermissions.length === 0) {
      return false;
    }
    return this.allPermissions.every((permission: any) => permission.isSelected);
  }

  toggleAllPermissions(event: any): void {
    const isChecked = event.target.checked;
    if (this.allPermissions) {
      this.allPermissions.forEach((permission: any) => {
        permission.isSelected = isChecked;
      });
    }
  }

  onPermissionChange(): void {
    this.cd.detectChanges();
  }



loadAllPermissions() {
  this.settingService.getAllPermissions().subscribe({
    next: (response) => {
      this.allPermissions = response.data ;
      this.cd.detectChanges();
    },
    error: (error) => {
      Swal.fire('Error loading permissions:', error);
    }
  });
}

  savePermissions(): void {
    const selectedPermissions = this.allPermissions
      .filter((permission: any) => permission.isSelected)
      .map((permission: any) => permission.name);
    this.settingService.updateRolePermissions(this.selectedRole.id, selectedPermissions).subscribe({
      next: () => {
        const roleIndex = this.roles.findIndex(r => r.id === this.selectedRole.id);
        if (roleIndex !== -1) {
          this.roles[roleIndex].permissions = selectedPermissions;
        }

        Swal.fire('Permissions updated successfully!').then(() => {
          this.closePermissionsModal();

          window.location.reload();
        });
      },
      error: (error) => {
        console.error('Error:', error);
        Swal.fire('Error updating permissions!').then(() => {
          this.closePermissionsModal();
        });
      }
    });
  }

  closePermissionsModal(): void {
    this.showPermissionsModal = false;
    this.selectedRole = null;
      this.allPermissions = this.allPermissions.map(permission => ({
      ...permission,
      isSelected: false
    }));
  }

}
