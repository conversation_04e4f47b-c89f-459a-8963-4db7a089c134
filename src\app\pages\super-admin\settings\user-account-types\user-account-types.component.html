<div class="row mb-8">
  <div class="col-12">
    <div class="card">
      <div class="card-body p-9">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-circle symbol-60px me-6">
              <div class="symbol-label bg-light-dark-blue">
                <i class="fas fa-users-cog fs-2x text-dark-blue"></i>
              </div>
            </div>
            <div>
              <h3 class="fs-2 fw-bold text-gray-900 mb-2">User Account Types</h3>
              <p class="fs-6 fw-semibold text-gray-600 mb-0">Manage users, permissions, and roles</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row justify-content-center g-6 g-xl-9">
  <div class="col-lg-4 col-md-6" *ngFor="let feature of accountFeatures">
    <app-account-management-card [faIcon]="feature.faIcon" [title]="feature.title" [description]="feature.description"
      [routePath]="feature.routePath" [count]="feature.count" [countLabel]="feature.countLabel"
      (cardClick)="onCardClick($event)">
    </app-account-management-card>
  </div>
</div>