import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-user-account-types',
  templateUrl: './user-account-types.component.html',
  styleUrls: ['./user-account-types.component.scss']
})
export class UserAccountTypesComponent implements OnInit {

  accountFeatures = [
    // {
    //   faIcon: 'fas fa-users-cog',
    //   title: 'Users Management',
    //   description: 'Manage system users, profiles and account information',
    //   backgroundColor: 'bg-light-primary',
    //   iconColor: 'text-primary',
    //   routePath: '/super-admin/user-account-types/users',
    //   count: '1,247',
    //   countLabel: 'Total Users'
    // },
    {
      faIcon: 'fas fa-user-shield',
      title: 'Permissions Control',
      description: 'Define and manage system permissions and access rights',
      backgroundColor: 'bg-light-success',
      iconColor: 'text-success',
      routePath: '/super-admin/user-account-types/permissions',
      count: '89',
      countLabel: 'Active Permissions'
    },
    {
      faIcon: 'fas fa-user-tag',
      title: 'Roles Management',
      description: 'Create and assign user roles with specific permissions',
      backgroundColor: 'bg-light-warning',
      iconColor: 'text-warning',
      routePath: '/super-admin/user-account-types/roles',
      count: '12',
      countLabel: 'System Roles'
    }
  ];

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  onCardClick(routePath: string): void {
    this.router.navigate([routePath]);
  }

}
