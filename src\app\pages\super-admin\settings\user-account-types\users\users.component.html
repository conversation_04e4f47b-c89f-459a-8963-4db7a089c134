<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="d-flex align-items-center">
              <i class="fas fa-users text-primary me-3 fs-2"></i>
              <div>
                <h3 class="mb-0">Users Management</h3>
                <span class="text-muted fs-6">Manage system users and their information</span>
              </div>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm me-2" (click)="goBack()">
              <i class="fas fa-arrow-left me-2"></i>
              Back
            </button>
            <button class="btn btn-primary btn-sm" (click)="addUser()">
              <i class="fas fa-plus me-2"></i>
              Add User
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Users Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">All Users</h3>
          <div class="card-toolbar">
            <div class="d-flex align-items-center">
              <div class="position-relative me-3">
                <i class="fas fa-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                <input type="text" class="form-control form-control-sm ps-10" placeholder="Search users...">
              </div>
              <select class="form-select form-select-sm" style="width: auto;">
                <option value="">All Roles</option>
                <option value="admin">Admin</option>
                <option value="broker">Broker</option>
                <option value="developer">Developer</option>
                <option value="client">Client</option>
              </select>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-row-dashed table-row-gray-300 gy-7">
              <thead>
                <tr class="fw-bold fs-6 text-gray-800">
                  <th>User</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Last Login</th>
                  <th class="text-end">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let user of users">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="symbol symbol-45px me-3">
                        <img [src]="user.avatar" alt="User Avatar" class="rounded">
                      </div>
                      <div>
                        <div class="fw-bold text-gray-800">{{ user.name }}</div>
                        <div class="text-muted fs-7">{{ user.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="getRoleBadgeClass(user.role)">{{ user.role }}</span>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="getStatusBadgeClass(user.status)">{{ user.status }}</span>
                  </td>
                  <td class="text-muted">{{ user.lastLogin }}</td>
                  <td class="text-end">
                    <div class="dropdown">
                      <button class="btn btn-sm btn-light btn-active-light-primary" type="button"
                        data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" (click)="editUser(user.id)">
                            <i class="fas fa-edit me-2"></i>Edit
                          </a></li>
                        <li><a class="dropdown-item text-danger" href="#" (click)="deleteUser(user.id)">
                            <i class="fas fa-trash me-2"></i>Delete
                          </a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>